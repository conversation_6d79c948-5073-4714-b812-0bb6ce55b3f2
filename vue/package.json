{"name": "backend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build && pnpm copy", "copy": "rimraf ../assets/web/* && cpy 'dist/**/*' '../assets/web'", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.3.10", "axios": "^0.27.2", "element-plus": "^2.9.1", "js-md5": "^0.7.3", "lodash": "^4.17.21", "moment": "^2.29.3", "node-sass": "^7.0.1", "pinia": "^2.3.0", "quill-blot-formatter": "^1.0.5", "sass-loader": "^12.6.0", "style-loader": "^3.3.1", "vue": "^3.2.25", "vue-i18n": "11.0.0-rc.1", "vue-router": "^4.0.15"}, "devDependencies": {"@types/js-md5": "^0.4.3", "@types/lodash": "^4.17.13", "@types/node": "^17.0.45", "@types/qs": "^6.9.7", "@vitejs/plugin-vue": "^2.3.1", "cpy-cli": "^5.0.0", "rimraf": "^6.0.1", "sass": "^1.51.0", "typescript": "^5.7.2", "vite": "^2.9.7", "vue-tsc": "^2.2.0"}}