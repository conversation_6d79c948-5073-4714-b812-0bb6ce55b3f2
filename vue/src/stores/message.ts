// 引入 defineStore 和其他必要的函数
import { defineStore } from "pinia";

// 使用 defineStore 创建一个名为 'message' 的 store
export const useMessageStore = defineStore("message", {
    // state 定义应用的状态
    state: () => ({
        msg: "",
    }),
    // getters 类似于 computed 属性
    getters: {
        message: (state) => state.msg,
    },
    // actions 可以包含异步逻辑和更改 state 的方法
    actions: {
        setMessage(newMsg: string) {
            this.msg = newMsg;
        },
        clearMessage() {
            this.msg = "";
        },
        updateMessage(newPart: string) {
            this.msg += ` ${newPart}`;
        },
    },
});
