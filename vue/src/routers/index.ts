import { createRouter, createWebHashHistory } from "vue-router";
import Login from '@/modules/logins/Login.vue';
import Home from '@/modules/homes/Home.vue';
import Device from '@/modules/devices/Device.vue';
import Dashboard from "@/modules/dashboards/Dashboard.vue";
import UnlimitedModeSetting from "@/modules/settings/unlimiteds/UnlimitedModeSetting.vue";
import System from "@/modules/systems/System.vue";
import Error from '@/modules/errors/Error.vue';

const routes: any[] = [
    { path: '/login', name: 'login', component: Login },
    {
        path: '/', name: 'home', component: Home, children: [
            { path: '/dashboard', name: 'dashboard', component: Dashboard },
            { path: '/device', name: 'device', component: Device },
            { path: '/unlimited', name: 'unlimited', component: UnlimitedModeSetting },
            { path: '/system', name: 'system', component: System },
        ]
    },
    { path: '/error', name: 'error', component: Error },
]

export const router = createRouter({
    history: createWebHashHistory(),
    routes: routes
})