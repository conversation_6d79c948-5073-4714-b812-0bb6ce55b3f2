import { createApp } from "vue";
import App from "./App.vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import { router } from "./routers";
import AppContext from "./infrastructures/appcontext";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import i18n from "./i18n";
import { createPinia } from 'pinia';
import { useMessageStore } from './stores/message';

const pinia = createPinia();
const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

app.use(ElementPlus, { size: "small", zIndex: 3000 });
app.use(router);
app.use(i18n);
app.use(pinia); // 使用 store 并提供注入键
app.mount("#app");

AppContext.router = router;
AppContext.globalMessage = useMessageStore();