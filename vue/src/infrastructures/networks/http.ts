import axios from "axios";
import AppContext from "../appcontext";
import Result from "./result";
import qs from 'qs';

export default class Http {

    // 请求队列
    requestList: any = [];

    constructor() {
        // axios.interceptors.request.use(
        //     config => {
        //         // 设置canceltoken
        //         let cancelToken = null;
        //         config.cancelToken = new axios.CancelToken((c: any) => { cancelToken = c; });

        //         this.addRequest(config.url, cancelToken, `请勿重复请求${config.url}`);
        //         return config;
        //     }
        // );

        // axios.interceptors.response.use(
        //     response => {
        //         setTimeout(() => {
        //             this.removeRequest(response.config.url), 1000
        //         })
        //         return response;
        //     }
        // );
    }

    addRequest(currentUrl: any, cancelToken: any, errorMsg: any) {
        const errorMessage = errorMsg || '请求出错拥堵';

        for (let index = 0; index < this.requestList.length; index++) {
            if (this.requestList[index] === currentUrl) {
                cancelToken(errorMessage);
                return;
            }
        }

        // 将当前请求加入执行队列
        this.requestList.push(currentUrl);
    }

    removeRequest(currentUrl: any) {
        for (let index = 0; index < this.requestList.length; index++) {
            if (this.requestList[index] === currentUrl) {
                this.requestList.splice(index, 1)
                break
            }
        }
    }

    async get(url: string): Promise<any> {
        if(AppContext.isDebug)
            console.log(`[请求]${url}`);

        let res = await axios.get(url, { headers: { 'Content-Type': 'application/json' } });
        if (res.status != 200) {
            if(AppContext.isDebug)
                console.log(`[回复]出错，返回内容:${JSON.stringify(res)}}`);
            throw (res.statusText);
        }
        if(AppContext.isDebug)
            console.log(`[回复]成功，返回内容:${JSON.stringify(res.data)}}`);

        return res.data;
    }

    async post(url: string, data: any): Promise<Result> {
        if (!data.token && AppContext.token && AppContext.token.token)
            data.token = AppContext.token.token;

        let request = JSON.stringify(data);
        if(AppContext.isDebug)
            console.log(`[请求]${url}  ${request}`);
        let res = await axios.post(url, data, { headers: { 'Content-Type': 'application/json' } });
        if (res.status != 200) {
            if(AppContext.isDebug)
                console.log(`[回复]出错，返回内容:${JSON.stringify(res)}}`);
            throw (res.statusText);
        }

        if(AppContext.isDebug)
            console.log(`[回复]成功，返回内容:${JSON.stringify(res.data)}}`);

        if (res.data.code === 401) {
            AppContext.router.replace("/login");
            return res.data;
        } else if (res.data.code === 403) {
            AppContext.globalMessage.setMessage(res.data.msg);
            AppContext.router.replace("/error");
            return res.data;
        }
        return res.data;
    }

    getUrlParams(): any {
        let url = window.location.href;
        if (!url) return;

        let paramStr = url.substring(url.indexOf("?") + 1, url.length);
        if (!paramStr) return;

        let result: any = {};
        let params: string[] = paramStr.split('&');
        params.forEach((item: string) => {
            let keyValue = item.split('=');
            result[keyValue[0]] = keyValue[1];
        });
        return result;
    }
}