import moment from "moment";

export default class LocalStorage {
    set(key: string, value: any) {
        let data : StorageData = {
            value: value,
            expired: moment('9999-12-31').toDate()
        }
        let json = JSON.stringify(data);
        localStorage.setItem(key, json);
    }

    setWithExpiredNumber(key: string, value: any, expired: number) {
        let data : StorageData = {
            value: value,
            expired: moment().add(expired, 's').toDate()
        }
        let json = JSON.stringify(data);
        localStorage.setItem(key, json);
    }

    setWithExpiredDate(key: string, value: any, expired: Date) {
        let data : StorageData = {
            value: value,
            expired: expired
        }
        let json = JSON.stringify(data);
        localStorage.setItem(key, json);
    }

    get(key: string) : any|null {
        let json : string|null = localStorage.getItem(key);
        if(!json) return null;

        let data : StorageData = JSON.parse(json);
        if(!data) return null;

        if(moment(data.expired).isBefore(new Date())) {
            localStorage.removeItem(key);
            return null;
        }

        return data.value;
    }

    remove(key: string) {
        localStorage.removeItem(key);
    }
}

class StorageData {
    value: any = null;
    expired: Date = new Date();
}