import Http from "./networks/http";
import LocalStorage from "./storages/storage";
import Messager from "./messagers/messager";
import StorageKey from "./storages/storagekey";

export default class AppContext {
    static baseUrl: string = "http://************:8080/"; //`http://${window.deviceIp}:8080/`;//
    static router: any | null = null;
    static http: Http = new Http();
    static storage: LocalStorage = new LocalStorage();
    static messager: Messager = new Messager();
    static isDebug: boolean = false;
    static globalMessage: any | null = null;
    static portNO: number = 0;

    static get token() {
        let cache: any = this.storage.get(StorageKey.token);
        if (!cache) {
            AppContext.router.replace("/login");
            return null;
        }
        return cache;
    }

    static set token(val: any) {
        this.storage.setWithExpiredNumber(StorageKey.token, val, val.expiry);
    }
}
