import { AxiosError } from "axios";
import { ElMessage } from "element-plus";
import { ElMessageBox } from "element-plus";

export default class Messager {
    success(msg: string) {
        ElMessage({
            message: msg,
            grouping: true,
            type: "success",
        });
    }

    error(msg: string) {
        if (<any>msg instanceof AxiosError) {
            msg = (<AxiosError>(<any>msg)).message;
        }
        msg = msg.toString().replace("Error: ", "");
        ElMessage({
            message: msg,
            grouping: true,
            type: "error",
        });
    }

    warn(msg: string) {
        ElMessage({
            message: msg,
            grouping: true,
            type: "warning",
        });
    }

    async confirm(title: string, msg: string): Promise<boolean> {
        return await ElMessageBox.confirm(msg, title, {
            confirmButtonText: "确认",
            cancelButtonText: "取消",
        })
            .then(() => {
                return true;
            })
            .catch(() => {
                return false;
            });
    }
}
