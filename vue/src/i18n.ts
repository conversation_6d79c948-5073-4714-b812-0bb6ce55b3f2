import { createI18n } from "vue-i18n";
import en from "./locales/en.json";
import zh from "./locales/zh.json";
import es from "./locales/es.json";

const langs = ['zh', 'en', 'es'];
let langCode = "zh";

// 从缓存里获取语言
let cache = localStorage.getItem("lang");
if (cache) {
    langCode = cache;
}
// 若缓存没有数据，则从浏览器设置里获取语言
else {
    try {
        const language = (navigator.language || "en").toLocaleLowerCase();
        langCode = language.split("-")[0];
        if (langs.indexOf(langCode) < 0) {
            langCode = "en";
        }
    } catch {
        langCode = "en";
    }
}

const i18n = createI18n({
    locale: langCode,
    fallbackLocale: "en",
    globalInjection: true,
    messages: { en, zh, es },
});

export default i18n;
