<template>
    <div>
        <div style="margin: 10px">
            <el-button type="danger" size="large" @click="resetAll">{{
                $t("复位所有设备")
            }}</el-button>
        </div>
        <div
            v-show="datas && datas.length > 0"
            class="container"
            v-loading="loading"
        >
            <div
                class="device-container"
                v-for="(device, index) in datas"
                :key="index"
            >
                <div class="device-item" :style="{ borderColor: device.color }">
                    <span>{{ device.score }}</span>
                    <span>{{ device.terminalNO }}</span>
                </div>
                <div class="device-info">
                    <div class="device-state">
                        <div
                            class="device-state-light"
                            :style="{
                                background: device.alive ? '#7ec050' : 'red',
                            }"
                        ></div>
                        <span>{{ $t(device.alive ? "在线" : "离线") }}</span>
                    </div>
                    <span>{{ device.address }}</span>
                </div>
                <div>
                    <el-button type="danger" @click="reset(device)">{{
                        $t("复位")
                    }}</el-button>
                </div>
            </div>
        </div>
        <div v-show="!datas || datas.length === 0" class="empty-container">
            <el-empty :description="$t('暂无设备')" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onBeforeUnmount, onMounted, Ref, ref } from "vue";
import AppContext from "@/infrastructures/appcontext";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 内部属性
let loading: Ref<boolean> = ref(false);
let datas: Ref<Array<any>> = ref<any[]>([]);
let interval: number = -1;

onMounted(async () => {
    await getlist();

    interval = setInterval(getlist, 5000);
});

onBeforeUnmount(() => {
    if (interval) {
        clearInterval(interval);
    }
});

// 内部方法
const getlist = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "device/list",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        for (let i = 0; i < res.data.length; i++) {
            let item = <any>res.data[i];
            if (datas.value && datas.value.length > 0) {
                let index = datas.value.findIndex(
                    (x) => x.terminalNO == item.terminalNO
                );
                if (index >= 0) {
                    datas.value[index].color = item.color;
                    datas.value[index].score = item.score;
                    datas.value[index].alive = item.alive;
                } else {
                    datas.value.push(item);
                }
            } else {
                datas.value.push(item);
            }
        }
        datas.value = res.data;
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const reset = async (device: any) => {
    try {
        let confirm = await AppContext.messager.confirm(
            t("提示"),
            t("确认复位当前设备？")
        );
        if (!confirm) return;

        loading.value = true;

        let request = {
            terminalNO: device.terminalNO,
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "device/reset",
            request
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const resetAll = async () => {
    try {
        let confirm = await AppContext.messager.confirm(
            t("提示"),
            t("确认复位所有设备？")
        );
        if (!confirm) return;

        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "device/resetAll",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start; /* 让多行内容顶部对齐 */
    align-items: flex-start; /* 确保单行内的项目也顶部对齐 */

    .device-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 200px;
        height: 200px;
        margin: 20px;

        .device-item {
            box-sizing: border-box; /* 确保 padding 和 border 包含在 width 和 height 内 */
            width: 100px;
            height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 50%; /* 使用百分比来创建圆形 */
            border: 6px solid grey;
            background: #000;
            color: white;
            font-weight: bold;
        }

        .device-info {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;

            .device-state {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 10px;

                .device-state-light {
                    height: 10px;
                    width: 10px;
                    border-radius: 100%; /* 使用百分比来创建圆形 */
                    background: red;
                    margin-right: 5px;
                }

                span {
                    font-size: 12px;
                }
            }
        }
    }
}

.empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}
</style>
