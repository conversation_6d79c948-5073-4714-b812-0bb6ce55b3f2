<template>
    <div class="container">
        <div class="login-container">
            <div class="login-left">
                <el-image
                    style="width: 80%; height: 80%"
                    :src="background"
                    fit="cover"
                />
            </div>
            <div class="login-right" v-loading="loading">
                <div class="logo">
                    <el-image
                        style="width: 160px; height: 160px"
                        :src="logo"
                        fit="contain"
                    />
                </div>
                <div class="title">
                    <span>{{ $t("运动宝") }}</span>
                </div>
                <div style="width: 70% !important">
                    <el-form ref="ruleFormRef" :model="form" size="large" label-width="120px">
                        <el-form-item :label="$t('账号')" prop="account">
                            <el-input
                                v-model="form.account"
                                size="large"
                                :placeholder="$t('请输入账号')"
                            />
                        </el-form-item>
                        <el-form-item :label="$t('密码')" prop="password">
                            <el-input
                                v-model="form.password"
                                size="large"
                                type="password"
                                :placeholder="$t('请输入密码')"
                                @keydown.enter.native="login"
                            />
                        </el-form-item>
                        <el-form-item>
                            <div class="button" @click="login">
                                <span style="font-weight: bold">{{
                                    $t("登录")
                                }}</span>
                            </div>
                        </el-form-item>
                        <el-form-item>
                            <div class="langauge-selector">
                                <el-popover
                                    placement="bottom"
                                    :width="100"
                                    trigger="click"
                                >
                                    <template #reference>
                                        <el-button type="primary" link
                                            >语言(Language)</el-button
                                        >
                                    </template>
                                    <div
                                        style="
                                            display: flex;
                                            justify-content: center;
                                            align-items: center;
                                            cursor: pointer;
                                            margin: 5px;
                                            padding: 5px;
                                        "
                                        v-for="option in langOptions"
                                        @click="setLang(option.value)"
                                    >
                                        <span>{{ option.label }}</span>
                                    </div>
                                </el-popover>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
        <div style="font-size: 14px">
            Copyright @ 2019-{{ currentYear }} Ice Butterfly All Right Reserved
            | {{ $t("广州冰蝶科技有限公司") }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, Ref, ref } from "vue";
import AppContext from "@/infrastructures/appcontext";
import md5 from "js-md5";
import logo from "@/assets/logo.png";
import background from "@/assets/login-background.png";
import { useI18n } from "vue-i18n";

const { locale } = useI18n();
const form = reactive({ account: "", password: "" });
const currentYear = ref(new Date().getFullYear());
const langOptions = [
    { value: "zh", label: "中文" },
    { value: "en", label: "English" },
];
let loading: Ref<boolean> = ref(false);

onMounted(async () => {
    await getLang();
    await getDeviceNO();
});

const getLang = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/language/get",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        if (locale.value != res.data) {
            locale.value = res.data;
            localStorage.setItem("lang", res.data);
        }
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const getDeviceNO = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/getDeviceNO",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.portNO = res.data;
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const setLang = async (lang: string) => {
    try {
        loading.value = true;

        let request = {
            langCode: lang,
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/language/set",
            request
        );
        if (!res.success) throw new Error(res.msg);

        locale.value = lang;
        localStorage.setItem("lang", lang);
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const login = async () => {
    try {
        loading.value = true;

        let request = {
            account: form.account,
            password: md5(form.password),
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/account/login",
            request
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.token = res.data;
        AppContext.router.replace("/");
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.container {
    height: 100%;

    .login-container {
        display: flex;
        height: calc(100% - 50px);
        justify-content: center;
        align-items: center;
        padding-left: 5%;
        padding-right: 5%;

        .login-left {
            flex: 3;
        }

        .login-right {
            flex: 2;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .logo {
                margin-top: -100px;
                margin-left: 100px;
            }

            .title {
                font-size: 20px;
                font-weight: bold;
                margin-left: 100px;
                margin-bottom: 50px;
                font-family: "Arial", "Microsoft YaHei", "黑体", "宋体",
                    sans-serif;
            }

            .button {
                display: flex;
                justify-content: center;
                align-items: center;
                color: #fff;
                cursor: pointer;
                border-radius: 5px;
                width: 100% !important;
                height: 45px !important;
                margin-top: 20px;
                box-shadow: 0px 2px 10px rgba(254, 110, 2, 0.8);
                background: linear-gradient(to right, #ff9505, #fe6e02);
            }

            .langauge-selector {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                margin-top: 40px;

                // .language-option {
                //     height: 60px;
                //     font-weight: bold;
                //     cursor: pointer;
                // }
            }
        }
    }
}
</style>
