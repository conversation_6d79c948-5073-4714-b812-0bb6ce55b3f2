<template>
    <el-dialog v-model="visible" width="50%" @closed="handleClose" draggable>
        <div class="container">
            <el-form
                ref="form"
                :model="formData"
                label-width="150px"
                size="large"
            >
                <el-form-item :label="$t('旧密码')" prop="oldPassword">
                    <el-input
                        v-model="formData.oldPassword"
                        type="password"
                        show-password
                    />
                </el-form-item>
                <el-form-item :label="$t('新密码')" prop="newPassword">
                    <el-input
                        v-model="formData.newPassword"
                        type="password"
                        show-password
                    />
                </el-form-item>
                <el-form-item :label="$t('确认密码')" prop="confirmPassword">
                    <el-input
                        v-model="formData.confirmPassword"
                        type="password"
                        show-password
                    />
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <el-button type="primary" @click="handleConfirm" size="large">{{
                $t("确定")
            }}</el-button>
            <el-button @click="handleClose" size="large">{{
                $t("取消")
            }}</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { reactive, Ref, ref } from "vue";
import AppContext from "@/infrastructures/appcontext";
import md5 from "js-md5";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

class RequestForm {
    oldPassword!: string;
    newPassword!: string;
    confirmPassword!: string;
}

// 内部属性
let form = ref();
let formData = reactive(new RequestForm());
let visible: Ref<boolean> = ref(false);

const show = () => {
    visible.value = true;
};

const handleClose = () => {
    form.value.resetFields();
    visible.value = false;
};

const handleConfirm = async () => {
    try {
        if (formData.newPassword != formData.confirmPassword)
            throw new Error(t("确认密码与新密码不一致，请重新输入"));

        let request = {
            oldPassword: md5(formData.oldPassword!),
            newPassword: md5(formData.newPassword!),
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/password/modify",
            request
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
        handleClose();
    } catch (ex: any) {
        AppContext.messager.error(t("保存失败，原因：") + ex);
    }
};

// =========================对外暴露==============================
defineExpose({
    show,
});
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    margin-right: 80px;
}
</style>
