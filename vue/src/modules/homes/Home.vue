<template>
    <div class="container">
        <el-container>
            <el-aside width="200px" style="display: flex; flex-direction: column;">
                <el-scrollbar style="flex-grow: 1;">
                    <div class="menu-header">
                        <el-image style="width: 30px; height: 30px" :src="logo" fit="cover" />
                        <span style="margin-left: 10px">SPORTBOX</span>
                    </div>
                    <el-menu text-color="#fff" background-color="#2b2d2e" active-text-color="#fff" router default-active="/dashboard">
                        <el-menu-item index="/dashboard">
                            <el-icon><odometer /></el-icon>
                            <span>{{$t('控制台')}}</span>
                        </el-menu-item>
                        <el-menu-item index="/device">
                            <el-icon><cpu /></el-icon>
                            <span>{{$t('设备管理')}}</span>
                        </el-menu-item>
                        <el-sub-menu index="5">
                            <template #title>
                                <el-icon>
                                    <Setting />
                                </el-icon>
                                <span>{{$t('模式参数')}}</span>
                            </template>
                            <el-menu-item index="/unlimited">{{$t('无限制模式')}}</el-menu-item>
                        </el-sub-menu>
                        <el-menu-item index="/system">
                            <el-icon><monitor /></el-icon>
                            <span>{{$t('系统设置')}}</span>
                        </el-menu-item>
                    </el-menu>
                </el-scrollbar>
            </el-aside>
            <el-container>
                <el-header height="40px" style="display: flex; justify-content: flex-end; align-items: center;">
                    <div class="header-right">
                        <el-dropdown @command="handleCommand" size="large">
                            <div class="header-right-content">
                                <el-avatar :size="30" :src="'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" style="margin-right: 10px" />
                                <span class="el-dropdown-link">
                                    {{$t('管理员')}}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </span>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="passwordModify"><span>
                                            <el-icon class="el-icon--left">
                                                <Key />
                                            </el-icon>{{$t('修改密码')}}
                                        </span></el-dropdown-item>
                                    <el-dropdown-item command="logout" divided>
                                        <el-icon class="el-icon--left">
                                            <SwitchButton />
                                        </el-icon>{{$t('退出登录')}}
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>
                <el-main>
                    <router-view style="height: 100%;" v-slot="{ Component }">
                        <keep-alive>
                            <component :is="Component" :key="$route.name" />
                        </keep-alive>
                    </router-view>
                </el-main>
            </el-container>
        </el-container>
        <PasswordModifyDialog ref="passwordModifyDialog"/>
    </div>
</template>

<script setup lang="ts">
import logo from '@/assets/logo.png';
import AppContext from '@/infrastructures/appcontext';
import { onMounted, ref } from 'vue';
import PasswordModifyDialog from './PasswordModifyDialog.vue';

let passwordModifyDialog = ref();

onMounted(() => {
    if(!AppContext.token || !AppContext.token.token || new Date(AppContext.token.expireDate) < new Date())
        AppContext.router.replace('/login');
})

const handleCommand = async (command: string | number | object) => {
    if(command === 'passwordModify') {
        passwordModifyDialog.value.show();
    }
    if(command === 'logout') {
        await logout();
    }
}

const logout = async() => {
    try {
        let res = await AppContext.http.post(AppContext.baseUrl + 'system/account/logout', {});
        if (!res.success) throw new Error(res.msg);

        AppContext.router.replace('/login');
    }
    catch (ex: any) {
        AppContext.messager.error(ex);
    }
}
</script>

<style lang="scss" scoped>
.container {
    height: 100%;

    .el-container {
        height: 100% !important;
        // background-color: #f5f5f5;
        .el-header {
            // border-bottom: 1px solid var(--el-border-color-light);
            box-shadow: 0px -1px 0 #f4f4f4 inset;
        }
    }

    .el-scrollbar {
        background-color: #2b2d2e !important;
    }

    .el-main {
        background-color: #fff;
    }

    .el-menu {
        height: 100% !important;
        background-color: #2b2d2e !important;
        border: none !important;

        .el-menu-item.is-active {
            background-color: #545c64 !important;
        }
    }

    .menu-header {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 70px;
        color: #fff;
        font-weight: bold;
    }

    .header-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .header-right-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    
    .bottom-button-container {
        margin-top: auto;
        padding: 10px;
        background-color: #2b2d2e; /* 与 el-aside 背景颜色一致 */
    }
}
</style>