<template>
    <div class="layout-container">
        <!-- 移动端顶部导航栏 -->
        <div class="mobile-header" v-if="isMobile">
            <div class="mobile-header-left">
                <el-button
                    class="menu-toggle-btn"
                    @click="toggleMobileMenu"
                    :icon="Menu"
                    circle
                    size="small"
                />
                <div class="mobile-logo">
                    <el-image style="width: 24px; height: 24px" :src="logo" fit="cover" />
                    <span>SPORTBOX</span>
                </div>
            </div>
            <div class="mobile-header-right">
                <el-dropdown @command="handleCommand" size="small">
                    <el-avatar :size="32" :src="'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="passwordModify">
                                <el-icon><Key /></el-icon>
                                {{$t('修改密码')}}
                            </el-dropdown-item>
                            <el-dropdown-item command="logout" divided>
                                <el-icon><SwitchButton /></el-icon>
                                {{$t('退出登录')}}
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>

        <!-- 移动端侧边栏遮罩 -->
        <div
            class="mobile-overlay"
            v-if="isMobile && showMobileMenu"
            @click="closeMobileMenu"
        ></div>

        <!-- 主布局容器 -->
        <el-container class="main-container">
            <!-- 侧边栏 -->
            <el-aside
                :width="asideWidth"
                :class="sidebarClasses"
                style="display: flex; flex-direction: column;"
            >
                <el-scrollbar style="flex-grow: 1;">
                    <!-- PC端菜单头部 -->
                    <div class="menu-header" v-if="!isMobile">
                        <el-image style="width: 30px; height: 30px" :src="logo" fit="cover" />
                        <span style="margin-left: 10px">SPORTBOX</span>
                    </div>

                    <!-- 菜单 -->
                    <el-menu
                        text-color="#fff"
                        background-color="#2b2d2e"
                        active-text-color="#fff"
                        router
                        default-active="/dashboard"
                        @select="onMenuSelect"
                        :class="{ 'mobile-menu': isMobile }"
                    >
                        <el-menu-item index="/dashboard">
                            <el-icon><odometer /></el-icon>
                            <span>{{$t('控制台')}}</span>
                        </el-menu-item>
                        <el-menu-item index="/device">
                            <el-icon><cpu /></el-icon>
                            <span>{{$t('设备管理')}}</span>
                        </el-menu-item>
                        <el-sub-menu index="5">
                            <template #title>
                                <el-icon>
                                    <Setting />
                                </el-icon>
                                <span>{{$t('模式参数')}}</span>
                            </template>
                            <el-menu-item index="/unlimited">{{$t('无限制模式')}}</el-menu-item>
                        </el-sub-menu>
                        <el-menu-item index="/system">
                            <el-icon><monitor /></el-icon>
                            <span>{{$t('系统设置')}}</span>
                        </el-menu-item>
                    </el-menu>
                </el-scrollbar>
            </el-aside>

            <!-- 右侧内容区域 -->
            <el-container>
                <!-- PC端头部 -->
                <el-header
                    height="40px"
                    v-if="!isMobile"
                    style="display: flex; justify-content: flex-end; align-items: center;"
                >
                    <div class="header-right">
                        <el-dropdown @command="handleCommand" size="large">
                            <div class="header-right-content">
                                <el-avatar :size="30" :src="'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" style="margin-right: 10px" />
                                <span class="el-dropdown-link">
                                    {{$t('管理员')}}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </span>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="passwordModify">
                                        <el-icon class="el-icon--left"><Key /></el-icon>
                                        {{$t('修改密码')}}
                                    </el-dropdown-item>
                                    <el-dropdown-item command="logout" divided>
                                        <el-icon class="el-icon--left"><SwitchButton /></el-icon>
                                        {{$t('退出登录')}}
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>

                <!-- 主内容区域 -->
                <el-main :class="{ 'mobile-main': isMobile }">
                    <router-view style="height: 100%;" v-slot="{ Component }">
                        <keep-alive>
                            <component :is="Component" :key="$route.name" />
                        </keep-alive>
                    </router-view>
                </el-main>
            </el-container>
        </el-container>

        <PasswordModifyDialog ref="passwordModifyDialog"/>
    </div>
</template>

<script setup lang="ts">
import logo from '@/assets/logo.png';
import AppContext from '@/infrastructures/appcontext';
import { onMounted, ref, computed, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';
import { Menu } from '@element-plus/icons-vue';
import PasswordModifyDialog from './PasswordModifyDialog.vue';

const { locale } = useI18n();
let passwordModifyDialog = ref();

// 移动端状态管理
const isMobile = ref(false);
const showMobileMenu = ref(false);

// 根据语言和设备类型计算侧边栏宽度
const asideWidth = computed(() => {
    if (isMobile.value) return '280px';

    // 根据当前语言调整宽度，解决西班牙语显示问题
    switch (locale.value) {
        case 'es': return '260px'; // 西班牙语需要更宽
        case 'en': return '220px'; // 英语稍宽
        case 'zh':
        default: return '200px';   // 中文默认宽度
    }
});

// 侧边栏样式类
const sidebarClasses = computed(() => ({
    'mobile-sidebar': isMobile.value,
    'mobile-sidebar-open': isMobile.value && showMobileMenu.value
}));

// 检测屏幕尺寸
const checkScreenSize = () => {
    isMobile.value = window.innerWidth <= 768;
    if (!isMobile.value) {
        showMobileMenu.value = false;
    }
};

// 切换移动端菜单
const toggleMobileMenu = () => {
    showMobileMenu.value = !showMobileMenu.value;
};

// 关闭移动端菜单
const closeMobileMenu = () => {
    showMobileMenu.value = false;
};

// 菜单选择时关闭移动端菜单
const onMenuSelect = () => {
    if (isMobile.value) {
        showMobileMenu.value = false;
    }
};

onMounted(() => {
    if(!AppContext.token || !AppContext.token.token || new Date(AppContext.token.expireDate) < new Date())
        AppContext.router.replace('/login');

    // 初始检测屏幕尺寸
    checkScreenSize();

    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', checkScreenSize);
});

const handleCommand = async (command: string | number | object) => {
    if(command === 'passwordModify') {
        passwordModifyDialog.value.show();
    }
    if(command === 'logout') {
        await logout();
    }
}

const logout = async() => {
    try {
        let res = await AppContext.http.post(AppContext.baseUrl + 'system/account/logout', {});
        if (!res.success) throw new Error(res.msg);

        AppContext.router.replace('/login');
    }
    catch (ex: any) {
        AppContext.messager.error(ex);
    }
}
</script>

<style lang="scss" scoped>
.layout-container {
    height: 100vh;
    overflow: hidden;
}

// 移动端顶部导航栏
.mobile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    padding: 0 16px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1001;

    .mobile-header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .menu-toggle-btn {
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            color: #606266;

            &:hover {
                background: #ecf5ff;
                border-color: #409eff;
                color: #409eff;
            }
        }

        .mobile-logo {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
        }
    }

    .mobile-header-right {
        .el-dropdown {
            cursor: pointer;
        }
    }
}

// 移动端遮罩层
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
}

// 主容器
.main-container {
    height: 100%;

    // PC端样式
    @media (min-width: 769px) {
        height: 100vh;
    }

    // 移动端样式
    @media (max-width: 768px) {
        height: calc(100vh - 56px); // 减去移动端头部高度
    }
}

// 侧边栏样式
.el-aside {
    transition: width 0.3s ease;

    .el-scrollbar {
        background-color: #2b2d2e !important;
    }

    // PC端侧边栏
    @media (min-width: 769px) {
        position: relative;
    }

    // 移动端侧边栏
    &.mobile-sidebar {
        position: fixed !important;
        top: 56px; // 移动端头部高度
        left: -280px;
        height: calc(100vh - 56px) !important;
        z-index: 1000;
        transition: left 0.3s ease;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

        &.mobile-sidebar-open {
            left: 0;
        }
    }
}

// 菜单样式
.el-menu {
    height: 100% !important;
    background-color: #2b2d2e !important;
    border: none !important;

    .el-menu-item.is-active {
        background-color: #545c64 !important;
    }

    // 确保菜单项文本不会被截断
    .el-menu-item,
    .el-sub-menu__title {
        white-space: nowrap;
        overflow: visible;

        span {
            overflow: visible;
            text-overflow: clip;
            white-space: nowrap;
        }
    }

    // 移动端菜单优化
    &.mobile-menu {
        .el-menu-item {
            height: 56px;
            line-height: 56px;
            font-size: 16px;

            .el-icon {
                font-size: 20px;
                margin-right: 12px;
            }
        }

        .el-sub-menu__title {
            height: 56px;
            line-height: 56px;
            font-size: 16px;

            .el-icon {
                font-size: 20px;
                margin-right: 12px;
            }
        }
    }
}

// PC端菜单头部
.menu-header {
    display: flex;
    justify-content: flex-start; // 左对齐
    align-items: center;
    height: 70px;
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
    padding-left: 20px;
}

// PC端头部
.el-header {
    box-shadow: 0px -1px 0 #f4f4f4 inset;

    .header-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .header-right-content {
            display: flex;
            justify-content: center;
            align-items: center;

            .el-dropdown-link {
                white-space: nowrap;
            }
        }
    }
}

// 主内容区域
.el-main {
    background-color: #fff;

    // PC端
    @media (min-width: 769px) {
        padding: 20px;
    }

    // 移动端
    &.mobile-main {
        padding: 16px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}
</style>