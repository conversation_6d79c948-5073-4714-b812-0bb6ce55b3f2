<template>
    <div class="container">
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-toggle" @click="toggleMobileMenu" v-if="isMobile">
            <el-icon :size="24"><Menu /></el-icon>
        </div>

        <!-- 移动端遮罩层 -->
        <div
            class="mobile-overlay"
            v-if="isMobile && showMobileMenu"
            @click="closeMobileMenu"
        ></div>

        <el-container>
            <el-aside
                :width="asideWidth"
                :class="{ 'mobile-aside': isMobile, 'mobile-aside-open': showMobileMenu }"
                style="display: flex; flex-direction: column;"
            >
                <el-scrollbar style="flex-grow: 1;">
                    <div class="menu-header">
                        <el-image style="width: 30px; height: 30px" :src="logo" fit="cover" />
                        <span style="margin-left: 10px">SPORTBOX</span>
                    </div>
                    <el-menu
                        text-color="#fff"
                        background-color="#2b2d2e"
                        active-text-color="#fff"
                        router
                        default-active="/dashboard"
                        @select="onMenuSelect"
                    >
                        <el-menu-item index="/dashboard">
                            <el-icon><odometer /></el-icon>
                            <span>{{$t('控制台')}}</span>
                        </el-menu-item>
                        <el-menu-item index="/device">
                            <el-icon><cpu /></el-icon>
                            <span>{{$t('设备管理')}}</span>
                        </el-menu-item>
                        <el-sub-menu index="5">
                            <template #title>
                                <el-icon>
                                    <Setting />
                                </el-icon>
                                <span>{{$t('模式参数')}}</span>
                            </template>
                            <el-menu-item index="/unlimited">{{$t('无限制模式')}}</el-menu-item>
                        </el-sub-menu>
                        <el-menu-item index="/system">
                            <el-icon><monitor /></el-icon>
                            <span>{{$t('系统设置')}}</span>
                        </el-menu-item>
                    </el-menu>
                </el-scrollbar>
            </el-aside>
            <el-container>
                <el-header
                    height="40px"
                    :class="{ 'mobile-header': isMobile }"
                    style="display: flex; justify-content: flex-end; align-items: center;"
                >
                    <div class="header-right">
                        <el-dropdown @command="handleCommand" size="large">
                            <div class="header-right-content">
                                <el-avatar
                                    :size="isMobile ? 24 : 30"
                                    :src="'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"
                                    style="margin-right: 10px"
                                />
                                <span class="el-dropdown-link">
                                    {{$t('管理员')}}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </span>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="passwordModify">
                                        <span class="dropdown-item-content">
                                            <el-icon class="el-icon--left">
                                                <Key />
                                            </el-icon>{{$t('修改密码')}}
                                        </span>
                                    </el-dropdown-item>
                                    <el-dropdown-item command="logout" divided>
                                        <span class="dropdown-item-content">
                                            <el-icon class="el-icon--left">
                                                <SwitchButton />
                                            </el-icon>{{$t('退出登录')}}
                                        </span>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>
                <el-main>
                    <router-view style="height: 100%;" v-slot="{ Component }">
                        <keep-alive>
                            <component :is="Component" :key="$route.name" />
                        </keep-alive>
                    </router-view>
                </el-main>
            </el-container>
        </el-container>
        <PasswordModifyDialog ref="passwordModifyDialog"/>
    </div>
</template>

<script setup lang="ts">
import logo from '@/assets/logo.png';
import AppContext from '@/infrastructures/appcontext';
import { onMounted, ref, computed, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';
import PasswordModifyDialog from './PasswordModifyDialog.vue';

const { locale } = useI18n();
let passwordModifyDialog = ref();

// 移动端检测
const isMobile = ref(false);
const showMobileMenu = ref(false);

// 根据语言和设备类型计算侧边栏宽度
const asideWidth = computed(() => {
    if (isMobile.value) return '280px';

    // 根据当前语言调整宽度
    switch (locale.value) {
        case 'es': return '260px'; // 西班牙语需要更宽
        case 'en': return '220px'; // 英语稍宽
        case 'zh':
        default: return '200px';   // 中文默认宽度
    }
});

// 检测屏幕尺寸
const checkScreenSize = () => {
    isMobile.value = window.innerWidth <= 768;
    if (!isMobile.value) {
        showMobileMenu.value = false;
    }
};

// 切换移动端菜单
const toggleMobileMenu = () => {
    showMobileMenu.value = !showMobileMenu.value;
};

// 关闭移动端菜单
const closeMobileMenu = () => {
    showMobileMenu.value = false;
};

// 菜单选择时关闭移动端菜单
const onMenuSelect = () => {
    if (isMobile.value) {
        showMobileMenu.value = false;
    }
};

onMounted(() => {
    if(!AppContext.token || !AppContext.token.token || new Date(AppContext.token.expireDate) < new Date())
        AppContext.router.replace('/login');

    // 初始检测屏幕尺寸
    checkScreenSize();

    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', checkScreenSize);
});

const handleCommand = async (command: string | number | object) => {
    if(command === 'passwordModify') {
        passwordModifyDialog.value.show();
    }
    if(command === 'logout') {
        await logout();
    }
}

const logout = async() => {
    try {
        let res = await AppContext.http.post(AppContext.baseUrl + 'system/account/logout', {});
        if (!res.success) throw new Error(res.msg);

        AppContext.router.replace('/login');
    }
    catch (ex: any) {
        AppContext.messager.error(ex);
    }
}
</script>

<style lang="scss" scoped>
.container {
    height: 100%;
    position: relative;

    .el-container {
        height: 100% !important;

        .el-header {
            box-shadow: 0px -1px 0 #f4f4f4 inset;
            position: relative;
            z-index: 999;

            &.mobile-header {
                padding-left: 60px; // 为移动端菜单按钮留出空间
            }
        }
    }

    .el-scrollbar {
        background-color: #2b2d2e !important;
    }

    .el-main {
        background-color: #fff;
        padding: 20px;

        @media (max-width: 768px) {
            padding: 10px;
        }
    }

    .el-menu {
        height: 100% !important;
        background-color: #2b2d2e !important;
        border: none !important;

        .el-menu-item.is-active {
            background-color: #545c64 !important;
        }

        // 确保菜单项文本不会被截断
        .el-menu-item,
        .el-sub-menu__title {
            white-space: nowrap;
            overflow: visible;

            span {
                overflow: visible;
                text-overflow: clip;
                white-space: nowrap;
            }
        }

        // 子菜单项样式调整
        .el-menu-item {
            padding-left: 40px !important;
        }
    }

    .menu-header {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 70px;
        color: #fff;
        font-weight: bold;
        white-space: nowrap;
    }

    .header-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .header-right-content {
            display: flex;
            justify-content: center;
            align-items: center;

            .el-dropdown-link {
                white-space: nowrap;

                @media (max-width: 768px) {
                    font-size: 14px;
                }
            }
        }
    }

    // 下拉菜单项内容样式
    .dropdown-item-content {
        display: flex;
        align-items: center;
        white-space: nowrap;
        min-width: 120px;
    }

    .bottom-button-container {
        margin-top: auto;
        padding: 10px;
        background-color: #2b2d2e;
    }
}

// 移动端样式
@media (max-width: 768px) {
    .mobile-menu-toggle {
        position: fixed;
        top: 8px;
        left: 15px;
        z-index: 1001;
        background: #2b2d2e;
        color: #fff;
        border-radius: 4px;
        padding: 8px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        &:hover {
            background: #3a3c3d;
        }
    }

    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }

    .mobile-aside {
        position: fixed !important;
        top: 0;
        left: -280px;
        height: 100vh !important;
        z-index: 1000;
        transition: left 0.3s ease;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

        &.mobile-aside-open {
            left: 0;
        }
    }
}

// PC端不同语言的宽度适配
@media (min-width: 769px) {
    .el-aside {
        transition: width 0.3s ease;
    }
}
</style>