<template>
    <div class="dashboard-container" v-loading="loading">
        <!-- PC端布局 -->
        <div class="pc-layout" v-if="!isMobile">
            <!-- 页面标题 -->
            <div class="page-title">
                <h1>{{ $t('游戏控制台') }}</h1>
                <p>{{ $t('选择您要执行的操作') }}</p>
            </div>

            <!-- 功能按钮区域 -->
            <div class="button-group">
                <!-- 启动游戏按钮 -->
                <div
                    class="btn-action btn-start"
                    :class="{ 'btn-disabled': isGameStarted }"
                    @click="handleStartGame"
                >
                    <div class="btn-icon">
                        <el-icon :size="80"><VideoPlay/></el-icon>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">
                            {{ isGameStarted ? $t('游戏进行中') : $t('启动游戏') }}
                        </span>
                        <span class="btn-subtitle">
                            {{ isGameStarted ? $t('游戏已开始') : $t('开始新的游戏') }}
                        </span>
                    </div>
                </div>

                <!-- 重置游戏按钮 -->
                <div class="btn-action btn-reset" @click="resetGame">
                    <div class="btn-icon">
                        <el-icon :size="80"><RefreshRight/></el-icon>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">{{ $t('重置游戏') }}</span>
                        <span class="btn-subtitle">{{ $t('重置游戏时间') }}</span>
                    </div>
                </div>
            </div>

            <!-- 状态信息 -->
            <div class="status-info">
                <div class="status-item">
                    <el-icon><InfoFilled/></el-icon>
                    <span>{{ $t('系统状态：正常运行') }}</span>
                </div>
            </div>
        </div>

        <!-- 移动端布局 -->
        <div class="mobile-layout" v-else>
            <!-- 游戏状态卡片 -->
            <div class="game-status-card">
                <div class="status-header">
                    <div class="status-indicator" :class="{ 'active': isGameStarted }">
                        <div class="status-dot"></div>
                        <span class="status-text">
                            {{ isGameStarted ? $t('游戏进行中') : $t('游戏未开始') }}
                        </span>
                    </div>
                    <div class="game-time" v-if="isGameStarted">
                        <el-icon><Timer/></el-icon>
                        <span>{{ formatGameTime(gameTime) }}</span>
                    </div>
                </div>
                <div class="status-info-mobile">
                    <el-icon><InfoFilled/></el-icon>
                    <span>{{ $t('系统状态：正常运行') }}</span>
                </div>
            </div>

            <!-- 快速操作区域 -->
            <div class="quick-actions">
                <h3>{{ $t('快速操作') }}</h3>

                <!-- 启动游戏按钮 -->
                <el-button
                    type="primary"
                    size="large"
                    :disabled="isGameStarted"
                    @click="handleStartGame"
                    class="mobile-action-btn start-btn"
                    :loading="loading && currentAction === 'start'"
                >
                    <template #icon>
                        <el-icon><VideoPlay/></el-icon>
                    </template>
                    {{ $t('启动游戏') }}
                </el-button>

                <!-- 重置游戏按钮 -->
                <el-button
                    type="danger"
                    size="large"
                    @click="resetGame"
                    class="mobile-action-btn reset-btn"
                    :loading="loading && currentAction === 'reset'"
                >
                    <template #icon>
                        <el-icon><RefreshRight/></el-icon>
                    </template>
                    {{ $t('重置游戏') }}
                </el-button>
            </div>

            <!-- 操作历史 -->
            <div class="operation-history" v-if="operationHistory.length > 0">
                <h3>{{ $t('最近操作') }}</h3>
                <div class="history-list">
                    <div
                        class="history-item"
                        v-for="(item, index) in operationHistory.slice(0, 3)"
                        :key="index"
                    >
                        <div class="history-icon">
                            <el-icon v-if="item.type === 'start'"><VideoPlay/></el-icon>
                            <el-icon v-else><RefreshRight/></el-icon>
                        </div>
                        <div class="history-content">
                            <div class="history-action">{{ item.action }}</div>
                            <div class="history-time">{{ item.time }}</div>
                        </div>
                        <div class="history-status" :class="item.status">
                            {{ $t(item.status === 'success' ? '成功' : '失败') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, Ref, ref } from "vue";
import { VideoPlay, RefreshRight, InfoFilled, Timer } from '@element-plus/icons-vue';
import AppContext from "@/infrastructures/appcontext";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 内部属性
let loading: Ref<boolean> = ref(false);
let interval: number = -1;
let isGameStarted: Ref<boolean> = ref(false);

// 移动端检测
const isMobile = ref(false);

// 移动端专用数据
const currentAction = ref(''); // 当前执行的操作
const gameTime = ref(0); // 游戏时长（秒）
const gameTimeInterval = ref<number | null>(null);
const operationHistory = ref<Array<{
    type: string;
    action: string;
    time: string;
    status: 'success' | 'error';
}>>([]);

// 检测屏幕尺寸
const checkScreenSize = () => {
    isMobile.value = window.innerWidth <= 768;
};

// 格式化游戏时间
const formatGameTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 添加操作历史
const addOperationHistory = (type: string, action: string, status: 'success' | 'error') => {
    const now = new Date();
    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    operationHistory.value.unshift({
        type,
        action,
        time: timeStr,
        status
    });

    // 只保留最近10条记录
    if (operationHistory.value.length > 10) {
        operationHistory.value = operationHistory.value.slice(0, 10);
    }
};

// 开始游戏时间计时
const startGameTimer = () => {
    if (gameTimeInterval.value) {
        clearInterval(gameTimeInterval.value);
    }

    gameTime.value = 0;
    gameTimeInterval.value = setInterval(() => {
        gameTime.value++;
    }, 1000);
};

// 停止游戏时间计时
const stopGameTimer = () => {
    if (gameTimeInterval.value) {
        clearInterval(gameTimeInterval.value);
        gameTimeInterval.value = null;
    }
    gameTime.value = 0;
};

onMounted(async () => {
    // 初始检测屏幕尺寸
    checkScreenSize();

    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize);

    // 启动状态检查定时器
    interval = setInterval(getState, 3000);
});

onBeforeUnmount(() => {
    if (interval) {
        clearInterval(interval);
    }
    if (gameTimeInterval.value) {
        clearInterval(gameTimeInterval.value);
    }
    window.removeEventListener('resize', checkScreenSize);
});

const getState = async () => {
    try {
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/started",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        const wasStarted = isGameStarted.value;
        isGameStarted.value = res.data;

        // 移动端游戏状态变化处理
        if (isMobile.value) {
            if (res.data && !wasStarted) {
                // 游戏刚开始
                startGameTimer();
            } else if (!res.data && wasStarted) {
                // 游戏结束
                stopGameTimer();
            }
        }
    } catch (ex: any) {
    }
};

const handleStartGame = () => {
    if (!isGameStarted.value) {
        startGame();
    }
};

const startGame = async () => {
    try {
        loading.value = true;
        currentAction.value = 'start';

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/start",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        // 添加操作历史
        addOperationHistory('start', t('启动游戏'), 'success');

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        // 添加失败记录
        addOperationHistory('start', t('启动游戏'), 'error');
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
        currentAction.value = '';
    }
};

const resetGame = async () => {
    try {
        let confirm = await AppContext.messager.confirm(t("提示"), t("确认重置游戏？"));
        if (!confirm) return;

        loading.value = true;
        currentAction.value = 'reset';

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/reset",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        // 添加操作历史
        addOperationHistory('reset', t('重置游戏'), 'success');

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        // 添加失败记录
        addOperationHistory('reset', t('重置游戏'), 'error');
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
        currentAction.value = '';
    }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

// PC端布局
.pc-layout {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100%;
    padding: 40px 20px;
}

// 移动端布局
.mobile-layout {
    padding: 16px;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

// 移动端游戏状态卡片
.game-status-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .status-indicator {
            display: flex;
            align-items: center;

            .status-dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #e74c3c;
                margin-right: 12px;
                transition: background-color 0.3s ease;
            }

            &.active .status-dot {
                background: #27ae60;
                animation: pulse 2s infinite;
            }

            .status-text {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
            }
        }

        .game-time {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #409eff;
            font-weight: 600;
            font-size: 14px;

            .el-icon {
                font-size: 16px;
            }
        }
    }

    .status-info-mobile {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #7f8c8d;
        font-size: 14px;

        .el-icon {
            font-size: 16px;
        }
    }
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

// 移动端快速操作区域
.quick-actions {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    h3 {
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
    }

    .mobile-action-btn {
        width: 100%;
        height: 56px;
        margin-bottom: 12px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;

        &:last-child {
            margin-bottom: 0;
        }

        &:active {
            transform: scale(0.98);
        }

        .el-icon {
            font-size: 20px;
        }

        &.start-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
            }

            &:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
                cursor: not-allowed;
                transform: none;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
        }

        &.reset-btn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;

            &:hover {
                background: linear-gradient(135deg, #ee82e9 0%, #f3455a 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(240, 147, 251, 0.4);
            }
        }
    }
}

// 移动端操作历史
.operation-history {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    h3 {
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
    }

    .history-list {
        .history-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
                border-bottom: none;
            }

            .history-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #f5f7fa;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;

                .el-icon {
                    font-size: 18px;
                    color: #606266;
                }
            }

            .history-content {
                flex: 1;

                .history-action {
                    font-size: 14px;
                    font-weight: 500;
                    color: #2c3e50;
                    margin-bottom: 2px;
                }

                .history-time {
                    font-size: 12px;
                    color: #909399;
                }
            }

            .history-status {
                font-size: 12px;
                padding: 2px 8px;
                border-radius: 12px;
                font-weight: 500;

                &.success {
                    background: #f0f9ff;
                    color: #67c23a;
                }

                &.error {
                    background: #fef0f0;
                    color: #f56c6c;
                }
            }
        }
    }
}

// PC端标题样式
.page-title {
    text-align: center;
    margin-bottom: 60px;

    h1 {
        font-size: 48px;
        font-weight: bold;
        color: #2c3e50;
        margin: 0 0 16px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
        font-size: 18px;
        color: #7f8c8d;
        margin: 0;
    }
}

// PC端按钮组
.button-group {
    display: flex;
    gap: 40px;
    margin-bottom: 60px;
    justify-content: center;
    flex-wrap: wrap;
}

// PC端按钮样式
.btn-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 280px;
    height: 280px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;

    &:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    &:active {
        transform: translateY(-4px);
    }

    &.btn-disabled {
        cursor: not-allowed;
        opacity: 0.6;

        &:hover {
            transform: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        &:active {
            transform: none;
        }

        .btn-icon {
            opacity: 0.5;
        }

        .btn-content {
            opacity: 0.7;
        }
    }

    .btn-icon {
        margin-bottom: 20px;
        color: #fff;
        opacity: 0.9;
    }

    .btn-content {
        text-align: center;
        color: #fff;

        .btn-title {
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .btn-subtitle {
            display: block;
            font-size: 14px;
            opacity: 0.8;
        }
    }
}

.btn-start {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
}

.btn-reset {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    &:hover {
        background: linear-gradient(135deg, #ee82e9 0%, #f3455a 100%);
    }
}

// PC端状态信息
.status-info {
    display: flex;
    justify-content: center;
    align-items: center;

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        color: #27ae60;
        font-size: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        white-space: nowrap;

        .el-icon {
            font-size: 18px;
        }
    }
}
</style>
