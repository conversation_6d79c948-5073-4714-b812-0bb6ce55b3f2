<template>
    <div class="container" v-loading="loading">
        <!-- 页面标题 -->
        <div class="page-title">
            <h1>{{ $t('游戏控制台') }}</h1>
            <p>{{ $t('选择您要执行的操作') }}</p>
        </div>

        <!-- 功能按钮区域 -->
        <div class="button-group">
            <!-- 启动游戏按钮 -->
            <div class="btn-action btn-start" @click="startGame">
                <div class="btn-icon">
                    <el-icon :size="80"><VideoPlay/></el-icon>
                </div>
                <div class="btn-content">
                    <span class="btn-title">{{ $t('启动游戏') }}</span>
                    <span class="btn-subtitle">{{ $t('开始新的游戏') }}</span>
                </div>
            </div>

            <!-- 重置游戏按钮 -->
            <div class="btn-action btn-reset" @click="resetGame">
                <div class="btn-icon">
                    <el-icon :size="80"><RefreshRight/></el-icon>
                </div>
                <div class="btn-content">
                    <span class="btn-title">{{ $t('重置游戏') }}</span>
                    <span class="btn-subtitle">{{ $t('重置游戏时间') }}</span>
                </div>
            </div>
        </div>

        <!-- 状态信息 -->
        <div class="status-info">
            <div class="status-item">
                <el-icon><InfoFilled/></el-icon>
                <span>{{ $t('系统状态：正常运行') }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, Ref, ref } from "vue";
import { VideoPlay, RefreshRight, InfoFilled } from '@element-plus/icons-vue';
import AppContext from "@/infrastructures/appcontext";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 内部属性
let loading: Ref<boolean> = ref(false);
let interval: number = -1;
let isGameStarted: Ref<boolean> = ref(false);

onMounted(async () => {
    interval = setInterval(getState, 3000);
});

const getState = async () => {
    try {
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/started",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        isGameStarted.value = res.data;
    } catch (ex: any) {
    }
};

const startGame = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/start",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const resetGame = async () => {
    try {
        let confirm = await AppContext.messager.confirm(t("提示"), t("确认重置游戏？"));
        if (!confirm) return;

        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/reset",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-title {
    text-align: center;
    margin-bottom: 60px;

    h1 {
        font-size: 48px;
        font-weight: bold;
        color: #2c3e50;
        margin: 0 0 16px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
        font-size: 18px;
        color: #7f8c8d;
        margin: 0;
    }
}

.button-group {
    display: flex;
    gap: 40px;
    margin-bottom: 60px;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: 30px;
    }
}

.btn-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 280px;
    height: 280px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;

    &:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    &:active {
        transform: translateY(-4px);
    }

    .btn-icon {
        margin-bottom: 20px;
        color: #fff;
        opacity: 0.9;
    }

    .btn-content {
        text-align: center;
        color: #fff;

        .btn-title {
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .btn-subtitle {
            display: block;
            font-size: 14px;
            opacity: 0.8;
        }
    }
}

.btn-start {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
}

.btn-reset {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    &:hover {
        background: linear-gradient(135deg, #ee82e9 0%, #f3455a 100%);
    }
}

.status-info {
    display: flex;
    justify-content: center;
    align-items: center;

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        color: #27ae60;
        font-size: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

        .el-icon {
            font-size: 18px;
        }
    }
}
</style>
