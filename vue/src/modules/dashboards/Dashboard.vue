<template>
    <div class="container" v-loading="loading">
        <div class="btn-reset" @click="resetGame">
            <el-icon :size="160"><RefreshRight/></el-icon>
            <span>{{$t('重置游戏')}}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, Ref, ref } from "vue";
import AppContext from "@/infrastructures/appcontext";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 内部属性
let loading: Ref<boolean> = ref(false);

onMounted(async () => {});

const resetGame = async () => {
    try {
        let confirm = await AppContext.messager.confirm(t("提示"), t("确认重置游戏？"));
        if (!confirm) return;

        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/reset",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.btn-reset {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    cursor: pointer;
    width: 300px !important;
    height: 300px !important;
    border-radius: 300px;
    box-shadow: 0px 5px 15px #e47470;
    background: linear-gradient(to right, #e47470, #e47470);
    span {
        font-size: 30px;
        font-weight: bold;
    }
}
</style>
