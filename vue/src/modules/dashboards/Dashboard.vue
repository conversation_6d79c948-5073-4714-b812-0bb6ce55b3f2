<template>
    <div class="container" v-loading="loading">
        <!-- 页面标题 -->
        <div class="page-title">
            <h1>{{ $t('游戏控制台') }}</h1>
            <p>{{ $t('选择您要执行的操作') }}</p>
        </div>

        <!-- 功能按钮区域 -->
        <div class="button-group">
            <!-- 启动游戏按钮 -->
            <div
                class="btn-action btn-start"
                :class="{ 'btn-disabled': isGameStarted }"
                @click="handleStartGame"
            >
                <div class="btn-icon">
                    <el-icon :size="80"><VideoPlay/></el-icon>
                </div>
                <div class="btn-content">
                    <span class="btn-title">
                        {{ isGameStarted ? $t('游戏进行中') : $t('启动游戏') }}
                    </span>
                    <span class="btn-subtitle">
                        {{ isGameStarted ? $t('游戏已开始') : $t('开始新的游戏') }}
                    </span>
                </div>
            </div>

            <!-- 重置游戏按钮 -->
            <div class="btn-action btn-reset" @click="resetGame">
                <div class="btn-icon">
                    <el-icon :size="80"><RefreshRight/></el-icon>
                </div>
                <div class="btn-content">
                    <span class="btn-title">{{ $t('重置游戏') }}</span>
                    <span class="btn-subtitle">{{ $t('重置游戏时间') }}</span>
                </div>
            </div>
        </div>

        <!-- 状态信息 -->
        <div class="status-info">
            <div class="status-item">
                <el-icon><InfoFilled/></el-icon>
                <span>{{ $t('系统状态：正常运行') }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, Ref, ref } from "vue";
import { VideoPlay, RefreshRight, InfoFilled } from '@element-plus/icons-vue';
import AppContext from "@/infrastructures/appcontext";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 内部属性
let loading: Ref<boolean> = ref(false);
let interval: number = -1;
let isGameStarted: Ref<boolean> = ref(false);

onMounted(async () => {
    interval = setInterval(getState, 3000);
});

onBeforeUnmount(() => {
    if (interval) {
        clearInterval(interval);
    }
});

const getState = async () => {
    try {
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/started",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        isGameStarted.value = res.data;
    } catch (ex: any) {
    }
};

const handleStartGame = () => {
    if (!isGameStarted.value) {
        startGame();
    }
};

const startGame = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/start",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const resetGame = async () => {
    try {
        let confirm = await AppContext.messager.confirm(t("提示"), t("确认重置游戏？"));
        if (!confirm) return;

        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/game/reset",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 40px); // 减去header高度
    padding: 40px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    @media (max-width: 768px) {
        min-height: calc(100vh - 60px); // 移动端调整
        padding: 20px 15px;
    }
}

.page-title {
    text-align: center;
    margin-bottom: 60px;

    h1 {
        font-size: 48px;
        font-weight: bold;
        color: #2c3e50;
        margin: 0 0 16px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);

        @media (max-width: 768px) {
            font-size: 36px;
        }

        @media (max-width: 480px) {
            font-size: 28px;
        }
    }

    p {
        font-size: 18px;
        color: #7f8c8d;
        margin: 0;

        @media (max-width: 768px) {
            font-size: 16px;
        }

        @media (max-width: 480px) {
            font-size: 14px;
        }
    }

    @media (max-width: 768px) {
        margin-bottom: 40px;
    }
}

.button-group {
    display: flex;
    gap: 40px;
    margin-bottom: 60px;
    justify-content: center;
    flex-wrap: wrap;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: 30px;
        margin-bottom: 40px;
    }

    @media (max-width: 480px) {
        gap: 20px;
    }
}

.btn-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 280px;
    height: 280px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;

    &:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    &:active {
        transform: translateY(-4px);
    }

    @media (max-width: 768px) {
        width: 260px;
        height: 260px;

        &:hover {
            transform: translateY(-4px);
        }

        &:active {
            transform: translateY(-2px);
        }
    }

    @media (max-width: 480px) {
        width: 240px;
        height: 240px;
    }

    @media (max-width: 360px) {
        width: 200px;
        height: 200px;
    }

    &.btn-disabled {
        cursor: not-allowed;
        opacity: 0.6;

        &:hover {
            transform: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        &:active {
            transform: none;
        }

        .btn-icon {
            opacity: 0.5;
        }

        .btn-content {
            opacity: 0.7;
        }
    }

    .btn-icon {
        margin-bottom: 20px;
        color: #fff;
        opacity: 0.9;

        @media (max-width: 480px) {
            margin-bottom: 15px;

            .el-icon {
                font-size: 60px !important;
            }
        }

        @media (max-width: 360px) {
            margin-bottom: 10px;

            .el-icon {
                font-size: 50px !important;
            }
        }
    }

    .btn-content {
        text-align: center;
        color: #fff;
        padding: 0 10px;

        .btn-title {
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            line-height: 1.2;

            @media (max-width: 480px) {
                font-size: 20px;
                margin-bottom: 6px;
            }

            @media (max-width: 360px) {
                font-size: 18px;
                margin-bottom: 4px;
            }
        }

        .btn-subtitle {
            display: block;
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.3;

            @media (max-width: 480px) {
                font-size: 12px;
            }

            @media (max-width: 360px) {
                font-size: 11px;
            }
        }
    }
}

.btn-start {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
}

.btn-reset {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    &:hover {
        background: linear-gradient(135deg, #ee82e9 0%, #f3455a 100%);
    }
}

.status-info {
    display: flex;
    justify-content: center;
    align-items: center;

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        color: #27ae60;
        font-size: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        white-space: nowrap;

        .el-icon {
            font-size: 18px;
        }

        @media (max-width: 768px) {
            padding: 10px 20px;
            font-size: 14px;

            .el-icon {
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            padding: 8px 16px;
            font-size: 12px;

            .el-icon {
                font-size: 14px;
            }
        }
    }
}
</style>
