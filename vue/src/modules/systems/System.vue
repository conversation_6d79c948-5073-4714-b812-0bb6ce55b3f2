<template>
    <div class="container" v-loading="loading">
        <el-form ref="form" label-width="150px" size="large">
            <el-form-item :label="$t('设备编号')">
                <span style="font-weight: bold">{{ AppContext.portNO }}</span>
            </el-form-item>
            <el-form-item :label="$t('调试模式')">
                <el-switch
                    v-model="debugEnable"
                    style="--el-switch-on-color: #13ce66"
                    @change="debugEnableSwitch"
                    size="large"
                />
            </el-form-item>
            <!-- <el-form-item :label="$t('回调地址')">
                <el-input
                    v-model="callback"
                    style="width: 400px"
                    :placeholder="$t('请输入回调地址')"
                    @input="debouncedSetCallbacks"
                    clearable
                />
            </el-form-item> -->
            <el-form-item :label="$t('背景图片')">
                <div
                    style="
                        display: flex;
                        flex-direction: column;
                        align-items: start;
                    "
                >
                    <div>
                        <span style="color: orange; margin-right: 10px"
                            >{{ $t("图片要求") }} 1080x1920 {{ $t("5MB以内") }}
                        </span>
                        <el-button
                            type="primary"
                            @click="resetBackground"
                            link
                            >{{ $t("重置") }}</el-button
                        >
                    </div>
                    <el-upload
                        accept=".jpg,.jpeg,.png,.bmp,.webp"
                        :data="uploadData"
                        :action="backgroundActionUrl"
                        :show-file-list="false"
                        :on-success="handleAvatarSuccess"
                        :before-upload="beforeAvatarUpload"
                        auto-upload
                    >
                        <img
                            v-if="backgroundUrl"
                            :src="backgroundUrl"
                            style="width: 108px; height: 192px"
                        />
                        <el-icon v-else class="avatar-uploader-icon"
                            ><Plus
                        /></el-icon>
                    </el-upload>
                </div>
            </el-form-item>
            <el-form-item :label="$t('语言(Language)')">
                <el-select
                    v-model="lang"
                    :placeholder="$t('请选择')"
                    style="width: 120px"
                    @change="setLang"
                >
                    <el-option
                        v-for="item in langOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('软件版本')">
                <span style="margin-right: 20px">v {{ version }}</span>
                <el-button type="primary" link @click="checkUpgrade"
                    ><el-icon><Refresh /></el-icon
                    ><span>{{ $t("检查更新") }}</span></el-button
                >
            </el-form-item>
            <el-form-item :label="$t('重启App')">
                <el-button type="danger" size="small" @click="restart">{{
                    $t("立即重启")
                }}</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onBeforeUnmount, onMounted, Ref, ref } from "vue";
import AppContext from "@/infrastructures/appcontext";
import { useI18n } from "vue-i18n";
import debounce from "lodash/debounce";
import type { UploadProps } from "element-plus";

const { t } = useI18n();
const { locale } = useI18n();
const langOptions = [
    { value: "zh", label: "中文" },
    { value: "en", label: "English" },
];
const validTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/bmp",
    "image/webp",
];
const backgroundActionUrl = `${AppContext.baseUrl}upload/background`;
const uploadData = { token: AppContext.token.token };

// 内部属性
let loading: Ref<boolean> = ref(false);
let debugEnable: Ref<boolean> = ref(false);
let lang = ref("");
let callback = ref("");
let version = ref("");
let backgroundUrl = ref("");

onMounted(async () => {
    getLang();
    await getDebugEnable();
    // await getCallbacks();
    await getVersion();
    await getBackground();
});

const getDebugEnable = async () => {
    try {
        loading.value = true;

        let request = {
            enable: debugEnable.value,
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/debug/get",
            request
        );
        if (!res.success) throw new Error(res.msg);

        debugEnable.value = res.data;
    } finally {
        loading.value = false;
    }
};

const debugEnableSwitch = async () => {
    try {
        loading.value = true;

        let request = {
            enable: debugEnable.value,
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/debug/set",
            request
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.messager.success(t("成功"));
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const restart = async () => {
    try {
        let confirm = await AppContext.messager.confirm(
            t("提示"),
            t("确认重启？")
        );
        if (!confirm) return;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/restart",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.router.replace("/login");
    } catch (ex: any) {
        AppContext.messager.error(ex);
    }
};

const getLang = async () => {
    try {
        loading.value = true;

        let langCode = localStorage.getItem("lang");
        if (!langCode) langCode = "zh";
        lang.value = langCode;
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const setLang = async (lang: string) => {
    try {
        loading.value = true;

        let request = {
            langCode: lang,
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/language/set",
            request
        );
        if (!res.success) throw new Error(res.msg);

        locale.value = lang;
        localStorage.setItem("lang", lang);
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const getCallbacks = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "setting/callback/get",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        if (res.data && (<[]>res.data).length > 0) {
            callback.value = res.data[0];
        }
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const setCallbacks = async () => {
    try {
        loading.value = true;

        let request = {
            callbacks: [callback.value],
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "setting/callback/set",
            request
        );
        if (!res.success) throw new Error(res.msg);
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const getVersion = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/version/get",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        version.value = res.data;
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const checkUpgrade = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/version/checkUpgrade",
            {}
        );
        if (!res.success) throw new Error(res.msg);
        console.log(res.data);

        if (res.data.needUpgrade) {
            let confirm = await AppContext.messager.confirm(
                t("提示"),
                t("发现新版本，是否升级到最新？")
            );
            if (confirm) await upgrade();
        } else {
            AppContext.messager.success(t("当前已是最新版本"));
        }
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const upgrade = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/version/upgrade",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        let confirm = await AppContext.messager.confirm(
            t("提示"),
            t("软件正在升级，需要重新登录，是否返回登录页？")
        );
        if (confirm) AppContext.router.replace("/login");
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const handleAvatarSuccess: UploadProps["onSuccess"] = (
    response,
    uploadFile
) => {
    if (!response.success) {
        AppContext.messager.error(response.msg);
        return;
    } else {
        backgroundUrl.value = URL.createObjectURL(uploadFile.raw!);
    }
};

const beforeAvatarUpload: UploadProps["beforeUpload"] = (rawFile) => {
    if (!validTypes.includes(rawFile.type)) {
        AppContext.messager.error(
            t("图片格式仅支持以下格式：.png/.jpg/.jpeg/.bmp/.webp")
        );
        return false;
    } else if (rawFile.size / 1024 / 1024 > 5) {
        AppContext.messager.error(t("图片大小不能超过5MB"));
        return false;
    }
    return true;
};

const getBackground = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/background/get",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        backgroundUrl.value = res.data;
    } finally {
        loading.value = false;
    }
};

const resetBackground = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/background/reset",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        backgroundUrl.value = res.data;
    } finally {
        loading.value = false;
    }
};

// 创建防抖版本的 setCallbacks 函数，延迟 500 毫秒
const debouncedSetCallbacks = debounce(setCallbacks, 500);
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start; /* 让多行内容顶部对齐 */
    align-items: flex-start; /* 确保单行内的项目也顶部对齐 */
}

.avatar-uploader ::v-deep .avatar {
    width: 178px !important;
    height: 178px !important;
    display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 108px;
    height: 192px;
    text-align: center;
    border: 1px dashed rgb(147, 145, 145);
    border-radius: 2px;
}
</style>
