<template>
    <div class="container" v-loading="loading">
        <el-form ref="form" :model="formData" label-width="160px" size="large">
            <el-form-item :label="$t('随机切换间隔')" prop="switchSplit">
                <el-input-number
                    v-model="formData.switchSplit"
                    :precision="0"
                />
                <span style="margin-left: 10px">{{ $t("秒") }}</span>
            </el-form-item>
            <el-form-item :label="$t('每局时长')" prop="switchSplit">
                <el-input-number
                    v-model="formData.minutesPerRound"
                    :precision="0"
                    :max="60"
                />
                <span style="margin-left: 10px">{{ $t("分钟") }}</span>
            </el-form-item>
            <el-form-item :label="$t('随机显示组合')" prop="displayArray">
                <div class="display-container">
                    <span class="tips">{{
                        $t("根据切换间隔，终端会在以下已勾选项随机切换")
                    }}</span>
                    <div
                        class="checkbox-container"
                        v-for="(dispaly, index) in formData.displayArray"
                    >
                        <el-checkbox
                            v-model="dispaly.enable"
                            style="margin: 10px"
                            :checked="dispaly.enable"
                            :label="dispaly.score"
                            :value="dispaly.score"
                            :key="dispaly.score"
                            size="large"
                        >
                            <template #default>
                                <span>{{
                                    $t("分数") + " " + dispaly.score
                                }}</span>
                            </template>
                        </el-checkbox>
                        <el-color-picker v-model="dispaly.color" />
                    </div>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleConfirm" size="large">{{
                    $t("保存")
                }}</el-button>
                <el-button type="danger" @click="handleReset" size="large">{{
                    $t("重置")
                }}</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, Reactive, reactive, Ref, ref } from "vue";
import AppContext from "@/infrastructures/appcontext";
import { UnlimitedForm } from "./UnlimitedForm";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 内部属性
let loading: Ref<boolean> = ref(false);
let formData = reactive(new UnlimitedForm());

onMounted(async () => {
    // 获取参数
    await getParams();
});

// 获取参数
const getParams = async () => {
    try {
        loading.value = true;

        let request = {
            mode: "Unlimited",
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "setting/mode/parameter/get",
            request
        );
        if (!res.success) throw new Error(res.msg);

        formData.set(res.data);
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

// 设置参数
const handleConfirm = async () => {
    try {
        loading.value = true;

        let request = {
            mode: "Unlimited",
            parameters: formData,
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "setting/mode/parameter/set",
            request
        );
        if (!res.success) throw new Error(res.msg);
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

// 重置参数
const handleReset = async () => {
    try {
        let confirm = await AppContext.messager.confirm(
            t("提示"),
            t("确认重置参数？")
        );
        if (!confirm) return;

        loading.value = true;

        let request = {
            mode: "Unlimited",
        };
        let res = await AppContext.http.post(
            AppContext.baseUrl + "setting/mode/parameter/reset",
            request
        );
        if (!res.success) throw new Error(res.msg);

        formData.displayArray = [];
        formData = new UnlimitedForm();
        formData.switchSplit = 30;
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
}

.display-container {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .checkbox-container {
        display: flex;
        flex-direction: row;
        align-items: center;

        span {
            margin-right: 20px;
        }
    }
}

.tips {
    color: rgb(141, 139, 139);
}
</style>
