class UnlimitedForm {
    switchSplit?: number = 30;
    minutesPerRound?: number = 5;
    displayArray?: UnlimitedDisplay[] = [];

    constructor() {
        for (let i = 0; i < 9; i++) {
            this.displayArray!.push(
                new UnlimitedDisplay(i + 1, "#00ff00", true)
            );
        }
    }

    set(entity: any) {
        if (entity) {
            this.switchSplit = entity.switchSplit;
            this.minutesPerRound = entity.minutesPerRound;
            this.displayArray = entity.displayArray ? entity.displayArray : [];
        }
    }
}

class UnlimitedDisplay {
    score?: number = 0;
    color?: string = "#00ff00";
    enable?: boolean = true;

    constructor(score: number, color: string, enable: boolean) {
        this.score = score;
        this.color = color;
        this.enable = enable;
    }
}

export { UnlimitedForm, UnlimitedDisplay };
