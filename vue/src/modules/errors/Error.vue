<template>
    <div class="container" v-loading="loading">
        <div class="msg-container">
            <el-icon size="150px" color="#ee5650"
                ><CircleCloseFilled
            /></el-icon>
            <div class="msg">
                <span>{{ msg }}</span>
                <span>[NO.{{ AppContext.portNO }}]</span>
            </div>
        </div>
        <div class="contact-container">
            <span class="email">{{ $t("邮箱：") }} <EMAIL></span>
            <span>
                Copyright @ 2019-{{ currentYear }} Ice Butterfly All Right
                Reserved | {{ $t("广州冰蝶科技有限公司") }}
            </span>
        </div>
    </div>
</template>

<script setup lang="ts">
import AppContext from "@/infrastructures/appcontext";
import { computed, nextTick, onBeforeUnmount, onMounted, Ref, ref } from "vue";

// 内部属性
let loading: Ref<boolean> = ref(false);
let interval: number = -1;

const currentYear = ref(new Date().getFullYear());
const msg = computed(() => AppContext.globalMessage.message);

onMounted(async () => {
    if (AppContext.portNO <= 0) {
        await getDeviceNO();
    }

    interval = setInterval(checkValid, 5000);
});

onBeforeUnmount(() => {
    if (interval) {
        clearInterval(interval);
    }
});

// 内部方法
const checkValid = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/valid",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        if (res.data === true) {
            AppContext.router.replace("/login");
        }
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};

const getDeviceNO = async () => {
    try {
        loading.value = true;

        let res = await AppContext.http.post(
            AppContext.baseUrl + "system/getDeviceNO",
            {}
        );
        if (!res.success) throw new Error(res.msg);

        AppContext.portNO = res.data;
    } catch (ex: any) {
        AppContext.messager.error(ex);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .msg-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 100px;

        .msg {
            font-size: 25px;
            margin-top: 40px;
            font-weight: bold;
            max-width: 50%;
        }
    }

    .contact-container {
        display: flex;
        flex-direction: column;
        margin-bottom: 20px;

        .email {
            margin-bottom: 10px;
        }
    }
}
</style>
